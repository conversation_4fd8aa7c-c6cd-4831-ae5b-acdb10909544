# Project Rules for AI Coding Assistants

## 1. Adhere to Coding Standards
- **Purpose**: Ensure consistency, readability, and maintainability of the codebase.
- **Guidelines**:
  - Follow the project's defined style guide (e.g., PEP 8 for Python, Airbnb JavaScript Style Guide).
  - Use linting tools (e.g., <PERSON><PERSON>int, Pylint) to enforce coding standards.
  - Write clear, descriptive comments for complex logic or non-obvious code sections.
  - Ensure that all code is modular, reusable, and adheres to the Single Responsibility Principle.
- **AI-Specific**:
  - When generating code, conform to the project's coding standards and linting rules.
  - Suggest improvements or refactorings to align existing code with standards.

## 2. Assist with Task Management
- **Purpose**: Help the development team stay organized, prioritize effectively, and meet deadlines.
- **Guidelines**:
  - Use task management tools (e.g., Jira, Trello) to track progress.
  - Prioritize tasks based on urgency, importance, and dependencies.
  - Provide reminders for upcoming deadlines or overdue tasks.
  - Break down large tasks into smaller, manageable subtasks.
- **AI-Specific**:
  - Analyze task dependencies and suggest optimal task sequences.
  - Generate task lists or subtasks based on project requirements or user stories.
  - Flag potential bottlenecks or delays based on task progress and team capacity.

## 3. Communicate Effectively
- **Purpose**: Ensure clear, timely, and constructive communication within the team.
- **Guidelines**:
  - Provide concise updates on project progress, task completion, and blockers.
  - Respond promptly to questions, feedback, or requests for clarification.
  - Use clear, non-technical language when communicating with non-technical stakeholders.
  - Document decisions, changes, and rationales in project documentation.
- **AI-Specific**:
  - Generate progress reports or summaries based on task status and code changes.
  - Draft responses to common queries or provide explanations for code logic.
  - Offer constructive feedback on code quality, suggesting improvements or optimizations.

## 4. Continuously Learn and Adapt
- **Purpose**: Improve the AI's coding skills, project knowledge, and overall effectiveness over time.
- **Guidelines**:
  - Regularly update the AI's knowledge base with new project information, code changes, and feedback.
  - Encourage the AI to learn from code reviews, bug reports, and user feedback.
  - Track the AI's performance and adjust its role or capabilities as needed.
- **AI-Specific**:
  - Refine code generation based on feedback from human developers.
  - Analyze past project data to identify patterns or areas for improvement.
  - Adapt task management suggestions based on team dynamics and project evolution.

## 5. Integrate into the Development Workflow
- **Purpose**: Seamlessly incorporate the AI into the team's development processes.
- **Guidelines**:
  - Assist with code reviews by identifying potential issues or suggesting improvements.
  - Support testing by generating test cases or identifying edge cases.
  - Aid in debugging by analyzing error logs or suggesting fixes.
  - Automate repetitive tasks (e.g., code formatting, dependency updates).
- **AI-Specific**:
  - Review code for adherence to standards and suggest optimizations.
  - Generate unit tests or integration tests for new features.
  - Provide debugging assistance by analyzing stack traces or logs.

---

## Rationale
These rules maximize the AI Coding Assistant's value while ensuring it complements human developers effectively. Adhering to coding standards maintains code quality and readability. Task management assistance helps the team stay organized and meet deadlines. Effective communication ensures transparency and collaboration. Continuous learning allows the AI to improve its contributions over time. Finally, integrating the AI into the development workflow leverages its strengths in automation, testing, and code review, enhancing overall productivity.